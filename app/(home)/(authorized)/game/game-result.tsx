import Image from "next/image";
import { useState } from "react";
import { GameResultButton } from "@/app/components/buttons/game-result-button";
import { FullScreenImage } from "@/app/components/full-screen";
import { Mask } from "@/app/components/mask";
import { GameId } from "@/app/constants";
import { imageUrl } from "@/utils/image-url";
import { DrawResultDialog } from "./draw-result-dialog";

enum DrawResult {
  None = "none",
  Win = "win",
  Lose = "lose",
}

export const GameResult = ({
  gameTitle,
  nickname,
  gameId,
  score,
  gameRecordId,
  onPlayAgain,
}: {
  gameTitle: React.ReactNode;
  nickname: string;
  gameId: GameId;
  score: number;
  gameRecordId?: string;
  onPlayAgain: () => void;
}) => {
  const [drawResult, setDrawResult] = useState<DrawResult>(DrawResult.None);
  const [couponCode, setCouponCode] = useState<string>("");
  const [isDrawing, setIsDrawing] = useState(false);

  const performDraw = async () => {
    if (!gameRecordId) {
      alert("無法進行抽獎，請重新遊戲");
      return;
    }

    setIsDrawing(true);
    try {
      const response = await fetch("/api/game/draw", {
        method: "POST",
        body: JSON.stringify({ gameRecordId }),
      });

      if (response.ok) {
        const result = await response.json();
        setDrawResult(
          result.result === "win" ? DrawResult.Win : DrawResult.Lose,
        );
        if (result.coupon) {
          setCouponCode(result.coupon.code);
        }
      } else {
        const error = await response.json();
        alert(error.message || "抽獎失敗");
      }
    } catch (error) {
      console.error("Draw error:", error);
      alert("網絡錯誤，請稍後再試");
    } finally {
      setIsDrawing(false);
    }
  };

  return (
    <>
      <FullScreenImage src={imageUrl("/screen-game.png")} />
      <div className="relative z-0 flex flex-col items-center pt-[4vw]">
        <Mask visible />
        <div className="z-0 w-[73vw] h-[23.5vw] flex justify-center items-center">
          {gameTitle}
        </div>
        <div className="relative z-0 mt-[5vw]">
          <Image
            className="w-[67.5vw]"
            unoptimized
            alt=""
            src={imageUrl("/game-result-background.png")}
            width={728}
            height={1001}
          />
          <div className="absolute w-full h-full left-0 top-0 flex flex-col py-[16.5vw] px-[10vw] font-[700]">
            <div>
              <span className="text-[8vw] text-[#fff100] font-[1000] mr-[3vw]">
                {nickname}
              </span>
              <span className="text-[5vw]">您在</span>
            </div>
            <div className="mt-[3vw]">
              <span className="text-[5vw]">
                {
                  {
                    balance: "極限平衡 中堅持了",
                    catch: "威風接招 中獲得",
                    quiz: "威金森考驗 中拿下",
                  }[gameId]
                }
              </span>
            </div>
            <div className="text-[#fff100] font-[1000] mt-[2vw]">
              <span className="text-[14vw]">{score}</span>
              <span className="text-[12vw] relative bottom-[0.5vw]">
                {
                  {
                    balance: "秒",
                    catch: "分",
                    quiz: "分",
                  }[gameId]
                }
              </span>
            </div>
            <div>
              <span className="text-[5vw]">
                {
                  {
                    balance: "實在太威啦!",
                    catch: "真的有夠威!",
                    quiz: "簡直強氣炸裂!",
                  }[gameId]
                }
              </span>
            </div>
          </div>
          <div className="flex gap-[2vw] justify-center mt-[2vw]">
            <GameResultButton
              onClick={performDraw}
              disabled={isDrawing || !gameRecordId}
            >
              {isDrawing ? "抽獎中..." : "分享立即抽"}
            </GameResultButton>
            <GameResultButton onClick={onPlayAgain}>再次挑戰</GameResultButton>
          </div>
        </div>
      </div>

      {drawResult !== DrawResult.None && (
        <DrawResultDialog
          onClose={() => setDrawResult(DrawResult.None)}
          onPlayAgain={onPlayAgain}
          drawResult={drawResult}
          code={couponCode || ""}
        />
      )}
    </>
  );
};
